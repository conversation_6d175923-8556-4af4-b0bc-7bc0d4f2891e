package com.mediacomm.controller.rpc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.BaseIntegrationTest;
import com.mediacomm.caesar.CaesarRunner;
import com.mediacomm.caesar.controller.CaesarFeignClientApi;
import com.mediacomm.caesar.controller.Vp7FeignClientApi;
import com.mediacomm.caesar.domain.CaesarPanelRect;
import com.mediacomm.caesar.domain.CaesarRedundantMode;
import com.mediacomm.caesar.domain.CaesarResponse;
import com.mediacomm.caesar.domain.CaesarSwitchBanner;
import com.mediacomm.caesar.domain.CaesarSwitchBottomImage;
import com.mediacomm.caesar.domain.CaesarVideoPanels;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.dao.KvmVideoWallDecoder;
import com.mediacomm.entity.message.PanelRect;
import com.mediacomm.entity.message.reqeust.body.PanelRectRequestBody;
import com.mediacomm.entity.message.reqeust.body.VwEnableRequestBody;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.BannerType;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.LayoutType;
import com.mediacomm.util.JsonUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.annotation.DirtiesContext;

import java.net.URI;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * KvmVideoWallRpc集成测试
 */
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class KvmVideoWallRpcIT extends BaseIntegrationTest {

    @LocalServerPort
    private int port;

    private TestRestTemplate restTemplate;

    @MockBean
    private CaesarFeignClientApi caesarFeignClientApi;

    @MockBean
    private Vp7FeignClientApi vp7FeignClientApi;

    private String baseUrl;
    private KvmMaster testMaster;
    private KvmVideoWall testVideoWall;

    @BeforeEach
    void setUp() {
        restTemplate = new TestRestTemplate();
        baseUrl = "http://localhost:" + port + "/skylink-api/rpc/video-wall";
        
        // 创建测试数据
        setupTestData();

        // 准备Caesar API响应
        CaesarVideoPanels caesarPanels = new CaesarVideoPanels();
        caesarPanels.setPanels(Collections.emptyList());

        when(caesarFeignClientApi.getVideoWallPanels(any(URI.class), eq(1)))
            .thenReturn(caesarPanels);
        when(caesarFeignClientApi.getRedundantMode(any(URI.class))).thenReturn(new CaesarRedundantMode());
    }
    
    @AfterEach
    void tearDown() {
        // 清理测试数据
        if (testVideoWall != null) {
            kvmVideoWallService.removeById(testVideoWall.getWallId());
        }
        if (testMaster != null) {
            kvmMasterService.removeById(testMaster.getMasterId());
        }
    }

    private void setupTestData() {
        // 创建测试主机
        testMaster = new KvmMaster();
        testMaster.setMasterId("test-master-001");
        testMaster.setDeviceIp("*************");
        testMaster.setName("Test Caesar Master");
        testMaster.setDeviceModel(DeviceType.CAESAR.getDeviceTypeId());
        testMaster.setVersion("1.0.0");
        testMaster.setCollectorProperties(Collections.emptyList());
        testMaster.setProperties(Collections.emptyList());
        kvmMasterService.save(testMaster);

        // 创建测试大屏
        testVideoWall = new KvmVideoWall();
        testVideoWall.setDeviceId(1);
        testVideoWall.setMasterId("test-master-001");
        testVideoWall.setName("Test Video Wall");
        testVideoWall.setDeviceModel(DeviceType.CAESAR_VP7_VIDEO_WALL.getDeviceTypeId());
        testVideoWall.setRowCount(2);
        testVideoWall.setColCount(2);
        testVideoWall.setSingleW(1920);
        testVideoWall.setSingleH(1080);
        testVideoWall.setUniqueSearchKey("VW-1");
        testVideoWall.setBannerType(BannerType.BANNER_VP7);
        
        // 添加解码器信息
        List<KvmVideoWallDecoder> decoders = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            KvmVideoWallDecoder decoder = new KvmVideoWallDecoder();
            decoder.setDeviceId(i + 1);
            decoder.setXpos((i % 2) * 1920);
            decoder.setYpos((i / 2) * 1080);
            decoder.setWidth(1920);
            decoder.setHeight(1080);
            decoders.add(decoder);
        }
        testVideoWall.setDecoders(decoders);
        
        // 添加VP7相关属性
        List<Property> properties = new ArrayList<>();
        testVideoWall.setProperties(properties);
        testVideoWall.setCollectorProperties(Collections.emptyList());
        
        kvmVideoWallService.save(testVideoWall);

        LambdaQueryWrapper<KvmVideoWall> wrapper = Wrappers.lambdaQuery(KvmVideoWall.class)
            .eq(KvmVideoWall::getDeviceId, testVideoWall.getDeviceId());
        testVideoWall = kvmVideoWallService.getOne(wrapper);
    }

    @Test
    @DisplayName("测试获取当前窗口 - 成功场景")
    void testGetCurrentVideoWallPanels_Success() {

        // 发送HTTP请求
        String url = baseUrl + "/panels?videoWallId=" + testVideoWall.getWallId();
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

        // 验证响应
        assertEquals(HttpStatus.OK, response.getStatusCode());
        
        Result<?> result = JsonUtils.decode(response.getBody(), new TypeReference<Result<?>>() {});
        assertNotNull(result);
        assertEquals(ResponseCode.EX_OK_200, result.getCode());
        

        // 验证Caesar API调用
        verify(caesarFeignClientApi, times(1))
            .getVideoWallPanels(any(URI.class), eq(1));
    }

    @Test
    @DisplayName("测试打开单个窗口 - 成功场景")
    void testOpenPanel_Success() {
        // 准备请求数据
        PanelRect panelRect = new PanelRect();
        panelRect.setPanelId(1);
        panelRect.setXpos(0);
        panelRect.setYpos(0);
        panelRect.setWidth(1920);
        panelRect.setHeight(1080);
        panelRect.setLayoutType(LayoutType.FULL_SCREEN);
        
        PanelRectRequestBody requestBody = new PanelRectRequestBody();
        requestBody.setPanelRect(panelRect);
        
        // 发送HTTP请求
        String url = baseUrl + "/open-panel?videoWallId=" + testVideoWall.getWallId();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<PanelRectRequestBody> request = new HttpEntity<>(requestBody, headers);

        CaesarResponse caesarResponse = new CaesarResponse();
        caesarResponse.setStatus(0);
        caesarResponse.setDescr("Success");

        when(caesarFeignClientApi.openVideoWallPanel(any(URI.class), eq(testVideoWall.getDeviceId()),
            any(CaesarPanelRect.class), nullable(String.class), nullable(String.class)))
            .thenReturn(caesarResponse);
        
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        // 验证响应
        assertEquals(HttpStatus.OK, response.getStatusCode());

        Result<?> result = JsonUtils.decode(response.getBody(), new TypeReference<Result<?>>() {});
        assertNotNull(result);
        assertEquals(ResponseCode.EX_OK_200, result.getCode());
        
        // 验证Caesar API调用
        verify(caesarFeignClientApi, times(1))
            .openVideoWallPanel(any(URI.class), eq(testVideoWall.getDeviceId()), any(CaesarPanelRect.class), 
                nullable(String.class), nullable(String.class));
    }

    @Test
    @DisplayName("测试关闭单个窗口 - 成功场景")
    void testClosePanel_Success() {
        // 准备请求数据
        PanelRect panelRect = new PanelRect();
        panelRect.setPanelId(1);
        
        PanelRectRequestBody requestBody = new PanelRectRequestBody();
        requestBody.setPanelRect(panelRect);

        // 准备Caesar API响应
        CaesarResponse caesarResponse = new CaesarResponse();
        caesarResponse.setStatus(0);
        caesarResponse.setDescr("Success");
        
        when(caesarFeignClientApi.closeVideoWallPanel(any(URI.class), eq(testVideoWall.getDeviceId()), 
            any(CaesarPanelRect.class), nullable(String.class), nullable(String.class)))
            .thenReturn(caesarResponse);

        // 发送HTTP请求
        String url = baseUrl + "/close-panel?videoWallId=" + testVideoWall.getWallId();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<PanelRectRequestBody> request = new HttpEntity<>(requestBody, headers);
        
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);

        // 验证响应
        assertEquals(HttpStatus.OK, response.getStatusCode());
        
        Result<?> result = JsonUtils.decode(response.getBody(), new TypeReference<Result<?>>() {});
        assertNotNull(result);
        assertEquals(ResponseCode.EX_OK_200, result.getCode());

        // 验证Caesar API调用
        verify(caesarFeignClientApi, times(1))
            .closeVideoWallPanel(any(URI.class), eq(testVideoWall.getDeviceId()), any(CaesarPanelRect.class),
                nullable(String.class), nullable(String.class));
    }

    @Test
    @DisplayName("测试开启或关闭大屏横幅 - VP7大屏")
    void testEnableBanner_VP7VideoWall() {
        // 准备请求数据
        VwEnableRequestBody requestBody = new VwEnableRequestBody();
        requestBody.setId(testVideoWall.getWallId());
        requestBody.setEnable(true);

        // 准备Caesar API响应
        CaesarResponse caesarResponse = new CaesarResponse();
        caesarResponse.setStatus(0);
        caesarResponse.setDescr("Success");

        when(caesarFeignClientApi.switchBanner(any(URI.class), eq(1),
            any(CaesarSwitchBanner.class), nullable(String.class), nullable(String.class)))
            .thenReturn(caesarResponse);

        // 发送HTTP请求
        String url = baseUrl + "/banner/enable";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<VwEnableRequestBody> request = new HttpEntity<>(requestBody, headers);

        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        // 验证响应
        assertEquals(HttpStatus.OK, response.getStatusCode());

        Result<?> result = JsonUtils.decode(response.getBody(), new TypeReference<Result<?>>() {});
        assertNotNull(result);
        assertEquals(ResponseCode.EX_OK_200, result.getCode());

        // 验证Caesar API调用
        verify(caesarFeignClientApi, times(1))
            .switchBanner(any(URI.class), eq(testVideoWall.getDeviceId()), any(CaesarSwitchBanner.class),
                nullable(String.class), nullable(String.class));
    }

    @Test
    @DisplayName("测试开启或关闭显示底图 - 成功场景")
    void testEnableBottomImage_Success() {
        // 准备请求数据
        VwEnableRequestBody requestBody = new VwEnableRequestBody();
        requestBody.setId(testVideoWall.getWallId());
        requestBody.setEnable(true);

        // 准备Caesar API响应
        CaesarResponse caesarResponse = new CaesarResponse();
        caesarResponse.setStatus(0);
        caesarResponse.setDescr("Success");

        when(caesarFeignClientApi.switchBannerBottomImage(any(URI.class), eq(1),
            any(CaesarSwitchBottomImage.class), nullable(String.class), nullable(String.class)))
            .thenReturn(caesarResponse);

        // 发送HTTP请求
        String url = baseUrl + "/bottom-image/enable";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<VwEnableRequestBody> request = new HttpEntity<>(requestBody, headers);

        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        // 验证响应
        assertEquals(HttpStatus.OK, response.getStatusCode());

        Result<?> result = JsonUtils.decode(response.getBody(), new TypeReference<Result<?>>() {});
        assertNotNull(result);
        assertEquals(ResponseCode.EX_OK_200, result.getCode());

        // 验证Caesar API调用
        verify(caesarFeignClientApi, times(1))
            .switchBannerBottomImage(any(URI.class), eq(testVideoWall.getDeviceId()), any(CaesarSwitchBottomImage.class),
                nullable(String.class), nullable(String.class));
    }

    @Test
    @DisplayName("测试大屏不存在的错误场景")
    void testVideoWallNotFound() {
        // 发送HTTP请求到不存在的大屏
        String url = baseUrl + "/panels?videoWallId=999";
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

        // 验证响应
        assertEquals(HttpStatus.OK, response.getStatusCode());

        Result<?> result = JsonUtils.decode(response.getBody(), new TypeReference<Result<?>>() {});
        assertNotNull(result);
        assertEquals(ResponseCode.EX_FAILURE_400, result.getCode());
        assertTrue(result.getMessage().contains("No exist videoWall!"));
    }

    @Test
    @DisplayName("测试Caesar API调用失败的场景")
    void testCaesarApiFailure() {
        // 准备Caesar API抛出异常
        when(caesarFeignClientApi.getVideoWallPanels(any(URI.class), eq(1)))
            .thenThrow(new RuntimeException("Caesar API connection failed"));

        // 发送HTTP请求
        String url = baseUrl + "/panels?videoWallId=" + testVideoWall.getWallId();
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

        // 验证响应
        assertEquals(HttpStatus.OK, response.getStatusCode());

        Result<?> result = JsonUtils.decode(response.getBody(), new TypeReference<Result<?>>() {});
        assertNotNull(result);
        assertEquals(ResponseCode.REMOTE_MSG_TIME_OUT, result.getCode());

        // 验证Caesar API被调用
        verify(caesarFeignClientApi, times(1))
            .getVideoWallPanels(any(URI.class), eq(1));
    }

}
